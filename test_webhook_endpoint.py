#!/usr/bin/env python3
"""
Test script to verify the Google Calendar webhook endpoint is working correctly.
"""

import requests
import json
from datetime import datetime


def test_webhook_endpoint():
    """Test the Google Calendar webhook endpoint."""
    print("🧪 Testing Google Calendar webhook endpoint...")
    
    # Test endpoint URL (adjust if needed)
    webhook_url = "http://localhost:8000/api/v1/webhooks/google-calendar"
    
    # Test 1: Sync/verification event (empty body)
    print("\n1. Testing sync/verification event (empty body)...")
    try:
        headers = {
            "X-Goog-Channel-ID": "test-channel-sync",
            "X-Goog-Resource-State": "sync",
            "X-Goog-Resource-ID": "test-resource-sync",
            "X-Goog-Resource-URI": "https://www.googleapis.com/calendar/v3/calendars/primary/events",
            "X-Goog-Message-Number": "1",
            "User-Agent": "APIs-Google; (+https://developers.google.com/webmasters/APIs-Google.html)"
        }
        
        response = requests.post(webhook_url, headers=headers, timeout=10)
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
        
        if response.status_code == 200:
            print("   ✅ PASS - Sync event handled correctly")
        else:
            print("   ❌ FAIL - Unexpected status code")
            
    except Exception as e:
        print(f"   ❌ FAIL - Error: {e}")
    
    # Test 2: Notification event (empty body)
    print("\n2. Testing notification event (empty body)...")
    try:
        headers = {
            "X-Goog-Channel-ID": "test-channel-notification",
            "X-Goog-Resource-State": "exists",
            "X-Goog-Resource-ID": "test-resource-notification",
            "X-Goog-Resource-URI": "https://www.googleapis.com/calendar/v3/calendars/primary/events",
            "X-Goog-Message-Number": "2",
            "User-Agent": "APIs-Google; (+https://developers.google.com/webmasters/APIs-Google.html)"
        }
        
        response = requests.post(webhook_url, headers=headers, timeout=10)
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
        
        if response.status_code == 200:
            print("   ✅ PASS - Notification event handled correctly")
        else:
            print("   ❌ FAIL - Unexpected status code")
            
    except Exception as e:
        print(f"   ❌ FAIL - Error: {e}")
    
    # Test 3: Event with JSON body
    print("\n3. Testing event with JSON body...")
    try:
        headers = {
            "Content-Type": "application/json",
            "X-Goog-Channel-ID": "test-channel-json",
            "X-Goog-Resource-State": "exists",
            "X-Goog-Resource-ID": "test-resource-json",
            "X-Goog-Resource-URI": "https://www.googleapis.com/calendar/v3/calendars/primary/events",
            "X-Goog-Message-Number": "3",
            "User-Agent": "APIs-Google; (+https://developers.google.com/webmasters/APIs-Google.html)"
        }
        
        payload = {
            "test": "data",
            "timestamp": datetime.now().isoformat()
        }
        
        response = requests.post(webhook_url, json=payload, headers=headers, timeout=10)
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
        
        if response.status_code == 200:
            print("   ✅ PASS - JSON event handled correctly")
        else:
            print("   ❌ FAIL - Unexpected status code")
            
    except Exception as e:
        print(f"   ❌ FAIL - Error: {e}")
    
    # Test 4: Event with invalid JSON body
    print("\n4. Testing event with invalid JSON body...")
    try:
        headers = {
            "Content-Type": "application/json",
            "X-Goog-Channel-ID": "test-channel-invalid",
            "X-Goog-Resource-State": "exists",
            "X-Goog-Resource-ID": "test-resource-invalid",
            "X-Goog-Resource-URI": "https://www.googleapis.com/calendar/v3/calendars/primary/events",
            "X-Goog-Message-Number": "4",
            "User-Agent": "APIs-Google; (+https://developers.google.com/webmasters/APIs-Google.html)"
        }
        
        # Send invalid JSON
        response = requests.post(
            webhook_url, 
            data="invalid json content", 
            headers=headers, 
            timeout=10
        )
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
        
        if response.status_code == 200:
            print("   ✅ PASS - Invalid JSON handled gracefully")
        else:
            print("   ❌ FAIL - Unexpected status code")
            
    except Exception as e:
        print(f"   ❌ FAIL - Error: {e}")
    
    print("\n🎉 Webhook endpoint testing completed!")
    print("\n💡 If any tests failed, make sure:")
    print("   1. The FastAPI server is running")
    print("   2. The server has been restarted after code changes")
    print("   3. The webhook endpoint URL is correct")


if __name__ == "__main__":
    test_webhook_endpoint()
