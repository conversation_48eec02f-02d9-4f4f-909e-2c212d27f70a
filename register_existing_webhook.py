#!/usr/bin/env python3
"""
Register existing webhook subscription with the local FastAPI adapter.
This script reads the webhook_subscription.json file and registers the channel
with the local Google Calendar adapter so it can process webhook events.
"""

import asyncio
import json
import sys
import uuid
from datetime import datetime, timezone
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.adapters.google_calendar import GoogleCalendarAdapter
from src.adapters.base import TriggerEventType


def load_existing_subscription():
    """Load existing webhook subscription information"""
    print("📄 Loading existing webhook subscription...")

    try:
        with open("webhook_subscription.json", "r") as f:
            subscription_info = json.load(f)

        print(f"✅ Found existing subscription:")
        print(f"   Channel ID: {subscription_info['channel_id']}")
        print(f"   Resource ID: {subscription_info['resource_id']}")
        print(f"   Calendar ID: {subscription_info['calendar_id']}")
        print(f"   Created: {subscription_info['created_at']}")
        print(f"   Expires: {subscription_info['expires_at']}")

        # Check if subscription is still valid
        expires_at = datetime.fromisoformat(subscription_info['expires_at'].replace('Z', '+00:00'))
        now = datetime.now(timezone.utc)
        
        if expires_at > now:
            time_left = expires_at - now
            print(f"   ✅ Status: Active (expires in {time_left.days} days)")
            return subscription_info
        else:
            print(f"   ⚠️ Status: EXPIRED")
            print(f"   💡 You need to create a new webhook subscription")
            return None

    except FileNotFoundError:
        print("❌ No webhook_subscription.json file found!")
        print("💡 Run register_webhook.py first to create a webhook subscription")
        return None
    except Exception as e:
        print(f"❌ Error loading subscription: {e}")
        return None


async def register_with_adapter(subscription_info):
    """Register the webhook channel with the local Google Calendar adapter"""
    print("\n🔧 Registering webhook with local FastAPI adapter...")

    try:
        # Create adapter instance
        adapter = GoogleCalendarAdapter()

        # Extract subscription details
        channel_id = subscription_info["channel_id"]
        resource_id = subscription_info["resource_id"]
        calendar_id = subscription_info["calendar_id"]
        
        # Convert expiration to timestamp
        expires_at = datetime.fromisoformat(subscription_info['expires_at'].replace('Z', '+00:00'))
        expiration_ms = int(expires_at.timestamp() * 1000)

        # Generate a test trigger ID
        test_trigger_id = uuid.uuid4()
        test_user_id = "test-user"

        print(f"   Registering channel: {channel_id}")
        print(f"   Resource ID: {resource_id}")
        print(f"   Calendar ID: {calendar_id}")
        print(f"   User ID: {test_user_id}")
        print(f"   Trigger ID: {test_trigger_id}")

        # Register the channel with the adapter
        adapter._active_channels[channel_id] = {
            "resource_id": resource_id,
            "expiration": expiration_ms,
            "trigger_id": test_trigger_id,
            "user_id": test_user_id,
            "calendar_id": calendar_id,
            "event_types": [
                TriggerEventType.CREATED,
                TriggerEventType.UPDATED,
                TriggerEventType.DELETED,
            ],
            "created_at": datetime.now(timezone.utc).timestamp(),
        }

        print(f"\n✅ Channel registered with local adapter!")
        print(f"   Active channels: {len(adapter._active_channels)}")

        # Verify registration
        if channel_id in adapter._active_channels:
            print(f"✅ Verification: Channel found in active channels")
            
            # Show channel details
            channel_info = adapter._active_channels[channel_id]
            print(f"\n📋 Channel Details:")
            print(f"   Channel ID: {channel_id}")
            print(f"   Resource ID: {channel_info['resource_id']}")
            print(f"   User ID: {channel_info['user_id']}")
            print(f"   Calendar ID: {channel_info['calendar_id']}")
            print(f"   Trigger ID: {channel_info['trigger_id']}")
            print(f"   Event Types: {[et.value for et in channel_info['event_types']]}")
            print(f"   Expires: {datetime.fromtimestamp(channel_info['expiration']/1000, timezone.utc)}")
            
            return True
        else:
            print(f"❌ Verification: Channel not found in active channels")
            return False

    except Exception as e:
        print(f"❌ Error registering with local adapter: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_webhook_processing():
    """Test webhook processing with the registered channel"""
    print("\n🧪 Testing webhook processing...")

    try:
        # Create adapter instance
        adapter = GoogleCalendarAdapter()

        # Get the registered channel
        if not adapter._active_channels:
            print("❌ No active channels found!")
            return False

        channel_id = list(adapter._active_channels.keys())[0]
        print(f"   Testing with channel: {channel_id}")

        # Create a mock webhook event
        mock_webhook_event = {
            "type": "notification",
            "webhook_headers": {
                "x-goog-channel-id": channel_id,
                "x-goog-resource-id": adapter._active_channels[channel_id]["resource_id"],
                "x-goog-resource-state": "exists",
                "x-goog-resource-uri": "https://www.googleapis.com/calendar/v3/calendars/primary/events",
                "x-goog-message-number": "1"
            }
        }

        print(f"   Processing mock webhook event...")
        result = await adapter.process_event(mock_webhook_event)

        if result is None:
            print(f"⚠️ Event processing returned None")
            print(f"   This is expected if no credentials are available or no recent events exist")
            print(f"   But the channel is now registered and will process real webhook events!")
        else:
            print(f"✅ Event processing successful: {result}")

        return True

    except Exception as e:
        print(f"❌ Error testing webhook processing: {e}")
        return False


async def main():
    print("🔧 Register Existing Webhook with Local Adapter")
    print("=" * 60)
    print(f"Timestamp: {datetime.now()}")
    print()

    # Step 1: Load existing subscription
    subscription_info = load_existing_subscription()
    if not subscription_info:
        print("\n❌ CRITICAL: No valid webhook subscription found!")
        print("\n💡 Next steps:")
        print("   1. Run 'python register_webhook.py' to create a new webhook subscription")
        print("   2. Or check if your existing subscription has expired")
        return False

    # Step 2: Register with local adapter
    success = await register_with_adapter(subscription_info)
    if not success:
        print("\n❌ CRITICAL: Failed to register with local adapter!")
        return False

    # Step 3: Test webhook processing
    await test_webhook_processing()

    print("\n🎉 WEBHOOK REGISTRATION COMPLETE!")
    print("=" * 60)
    print("\n✅ What was accomplished:")
    print("   ✅ Existing webhook subscription loaded")
    print("   ✅ Channel registered with local FastAPI adapter")
    print("   ✅ Webhook processing tested")

    print("\n🧪 Test the webhook now:")
    print("   1. Go to Google Calendar")
    print("   2. Create, update, or delete a calendar event")
    print("   3. Check your FastAPI logs for webhook notifications")
    print("   4. Check the database for stored calendar events:")
    print("      python debug_webhook_processing.py")

    print("\n📊 Monitor webhook activity:")
    print("   - FastAPI server logs will show incoming webhook events")
    print("   - Database will store complete event data")
    print("   - Events will be processed and trigger workflows")

    return True


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        if success:
            print("\n✅ All done! Your webhook is now ready to process events.")
        else:
            print("\n❌ Setup incomplete. Please resolve the issues and try again.")
    except KeyboardInterrupt:
        print("\n🛑 Registration interrupted by user")
    except Exception as e:
        print(f"\n❌ Registration failed: {e}")
        sys.exit(1)
