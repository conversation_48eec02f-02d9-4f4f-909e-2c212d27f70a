#!/usr/bin/env python3
"""
Test the webhook endpoint with the real channel ID from webhook_subscription.json
"""

import json
import requests
from datetime import datetime


def load_webhook_subscription():
    """Load the real webhook subscription info"""
    try:
        with open("webhook_subscription.json", "r") as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ No webhook_subscription.json file found!")
        return None
    except Exception as e:
        print(f"❌ Error loading subscription: {e}")
        return None


def test_webhook_with_real_channel():
    """Test the webhook endpoint with real channel information"""
    print("🧪 Testing Google Calendar webhook with real channel...")
    
    # Load real subscription info
    subscription = load_webhook_subscription()
    if not subscription:
        return False
    
    print(f"📋 Using real subscription:")
    print(f"   Channel ID: {subscription['channel_id']}")
    print(f"   Resource ID: {subscription['resource_id']}")
    print(f"   Calendar ID: {subscription['calendar_id']}")
    
    # Test endpoint URL (adjust if needed)
    webhook_url = "http://localhost:8000/api/v1/webhooks/google-calendar"
    
    # Test with real channel information
    print(f"\n🚀 Testing webhook endpoint: {webhook_url}")
    
    try:
        headers = {
            "X-Goog-Channel-ID": subscription['channel_id'],
            "X-Goog-Resource-State": "exists",
            "X-Goog-Resource-ID": subscription['resource_id'],
            "X-Goog-Resource-URI": f"https://www.googleapis.com/calendar/v3/calendars/{subscription['calendar_id']}/events",
            "X-Goog-Message-Number": "1",
            "User-Agent": "APIs-Google; (+https://developers.google.com/webmasters/APIs-Google.html)"
        }
        
        print(f"📨 Sending webhook notification...")
        print(f"   Headers: {headers}")
        
        response = requests.post(webhook_url, headers=headers, timeout=10)
        
        print(f"\n📊 Response:")
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
        
        if response.status_code == 200:
            print("\n✅ SUCCESS! Webhook processed successfully")
            print("🔍 Check the following:")
            print("   1. FastAPI server logs for processing details")
            print("   2. Database for new calendar events:")
            print("      python debug_webhook_processing.py")
            return True
        else:
            print(f"\n❌ FAIL - Unexpected status code: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"\n❌ FAIL - Cannot connect to webhook endpoint")
        print(f"💡 Make sure:")
        print(f"   1. FastAPI server is running: python -m src.main")
        print(f"   2. Server is accessible on localhost:8000")
        return False
    except Exception as e:
        print(f"\n❌ FAIL - Error: {e}")
        return False


def main():
    print("🔧 Google Calendar Webhook Test with Real Channel")
    print("=" * 60)
    print(f"Timestamp: {datetime.now()}")
    print()
    
    success = test_webhook_with_real_channel()
    
    if success:
        print("\n🎉 Test completed successfully!")
        print("\n📋 What happened:")
        print("   ✅ Webhook notification sent to FastAPI server")
        print("   ✅ Server processed the webhook with real channel ID")
        print("   ✅ If configured correctly, events should be fetched and stored")
        
        print("\n🔍 Next steps:")
        print("   1. Check server logs for detailed processing information")
        print("   2. Run: python debug_webhook_processing.py")
        print("   3. Create a real calendar event to test end-to-end")
    else:
        print("\n❌ Test failed!")
        print("\n🔧 Troubleshooting:")
        print("   1. Ensure FastAPI server is running")
        print("   2. Check if webhook subscription is valid")
        print("   3. Verify server startup loaded the webhook subscription")


if __name__ == "__main__":
    main()
