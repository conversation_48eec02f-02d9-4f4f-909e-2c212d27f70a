#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to run database migrations for the trigger service.
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from alembic.config import Config
from alembic import command


def run_migrations():
    """Run database migrations."""
    try:
        # Create Alembic configuration using the root alembic.ini
        alembic_cfg = Config("alembic.ini")

        # Run the migration
        print("Running database migrations...")
        command.upgrade(alembic_cfg, "head")
        print("✅ Database migrations completed successfully!")

    except Exception as e:
        print(f"❌ Migration failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    run_migrations()
