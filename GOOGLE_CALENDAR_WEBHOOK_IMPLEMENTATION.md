# Google Calendar Webhook Implementation

## Overview

This document describes the enhanced Google Calendar webhook implementation for the workflow automation platform trigger service. The implementation has been updated to properly handle webhook notifications by fetching complete event data manually and storing it in the database.

## Key Features

### 1. Enhanced Webhook Event Processing
- **Complete Event Data Fetching**: When a webhook notification is received, the system fetches complete event details using the Google Calendar API (since Google only sends minimal data in webhook payloads)
- **Database Storage**: All webhook events and complete event data are stored in a dedicated `calendar_events` table
- **Multi-user Support**: Proper handling of multiple users with individual credential management

### 2. Database Schema
- **New Table**: `calendar_events` table to store and track user calendar events
- **Proper Indexing**: Optimized indexes for efficient queries
- **Metadata Tracking**: Stores channel_id, resource_id, user_id, event_id, and complete event data
- **Timestamps**: Tracks both system timestamps and Google Calendar timestamps

### 3. Simplified Architecture
- **Removed Auth Module**: Eliminated the separate Auth service dependency as requested
- **Clean Codebase**: Removed unused/dead code and simplified the implementation
- **Core Focus**: Concentrated on essential Google Calendar integration functionality

## Database Schema

### CalendarEvent Table

```sql
CREATE TABLE calendar_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    channel_id VARCHAR(255) NOT NULL,
    resource_id VARCHAR(255) NOT NULL,
    user_id VARCHAR(255) NOT NULL,
    event_id VARCHAR(255) NOT NULL,
    calendar_id VARCHAR(255) NOT NULL,
    event_data JSON NOT NULL,
    event_type VARCHAR(50) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    google_created_at TIMESTAMP WITH TIME ZONE,
    google_updated_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(user_id, event_id, calendar_id)
);
```

### Indexes
- `idx_calendar_events_channel_id`
- `idx_calendar_events_resource_id`
- `idx_calendar_events_user_id`
- `idx_calendar_events_event_id`
- `idx_calendar_events_calendar_id`
- `idx_calendar_events_event_type`
- `idx_calendar_events_created_at`
- `idx_calendar_events_google_created_at`
- `idx_calendar_events_user_calendar`

## Implementation Details

### Webhook Processing Flow

1. **Webhook Received**: Google Calendar sends a webhook notification to `/api/v1/webhooks/google-calendar`
2. **Event Validation**: Validate webhook authenticity and extract metadata
3. **Fetch Complete Data**: Use Google Calendar API to fetch complete event details
4. **Store in Database**: Save complete event data in the `calendar_events` table
5. **Process Triggers**: Create trigger events and send to workflow execution

### Key Methods

#### `_process_calendar_notification()`
- Enhanced to fetch complete event data from Google Calendar API
- Stores events in database with proper error handling
- Filters events based on configured event types

#### `_fetch_recent_calendar_events()`
- Fetches recent calendar events from Google Calendar API
- Retrieves complete event details for each event
- Handles API errors gracefully

#### `_store_calendar_event_in_database()`
- Stores complete calendar event data in the database
- Implements upsert behavior (update if exists, create if new)
- Parses Google Calendar timestamps properly

## Configuration

### Required Settings
```python
# Google Calendar webhook URL (must be HTTPS)
GOOGLE_CALENDAR_WEBHOOK_URL = "https://your-domain.com/api/v1/webhooks/google-calendar"

# Optional webhook secret for verification
GOOGLE_CALENDAR_WEBHOOK_SECRET = "your-secret-key"
```

### Trigger Configuration
```json
{
    "calendar_id": "primary",
    "webhook_ttl": 604800,
    "event_filters": {},
    "use_polling": false
}
```

## Usage

### 1. Run Database Migration
```bash
python run_migration.py
```

### 2. Start the Service
```bash
python -m src.main
```

### 3. Test the Implementation
```bash
python test_webhook_implementation.py
```

## API Endpoints

### Webhook Endpoint
- **URL**: `POST /api/v1/webhooks/google-calendar`
- **Purpose**: Receives Google Calendar webhook notifications
- **Response**: `{"status": "accepted", "message": "Webhook received and queued for processing"}`

### Verification Endpoint
- **URL**: `GET /api/v1/webhooks/google-calendar/verify`
- **Purpose**: Webhook verification for Google Calendar
- **Response**: `{"status": "verified", "message": "Google Calendar webhook endpoint is active"}`

## Error Handling

### Database Errors
- Proper error logging for database operations
- Graceful handling of connection issues
- Upsert behavior to handle duplicate events

### API Errors
- Retry logic for transient Google Calendar API failures
- Fallback to partial event data if complete fetch fails
- Comprehensive error logging with correlation IDs

### Webhook Validation
- Signature verification if webhook secret is configured
- Required header validation
- Resource state validation

## Logging

The implementation includes comprehensive logging:
- Webhook receipt and processing
- Event fetching and storage
- Error conditions with correlation IDs
- Performance metrics

## Testing

The implementation includes a test script that verifies:
- Configuration validation
- Webhook event processing
- Database storage and retrieval
- Health check functionality

## Migration from Previous Implementation

1. **Database Migration**: Run the migration script to create the new table
2. **Remove Old Files**: The old `google_calender_webhook.py` file has been removed
3. **Update Configuration**: Ensure webhook URL is properly configured
4. **Test Integration**: Use the test script to verify functionality

## Security Considerations

- **HTTPS Required**: Google Calendar webhooks require HTTPS endpoints
- **Signature Verification**: Optional webhook signature verification
- **Credential Management**: Secure handling of Google OAuth2 credentials
- **Input Validation**: Proper validation of webhook payloads

## Performance Optimizations

- **Async Processing**: All database and API operations are asynchronous
- **Efficient Queries**: Optimized database indexes for fast lookups
- **Batch Processing**: Events are processed in batches when possible
- **Connection Pooling**: Database connection pooling for better performance
