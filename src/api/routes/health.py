"""
Health check API routes.

This module contains health check endpoints for monitoring service status
and dependencies.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Dict, Any, List
from datetime import datetime

from src.core.trigger_manager import TriggerManager
from src.database.connection import db_manager
from src.schemas.trigger import AdapterHealthResponse
from src.utils.logger import get_logger
from src.utils.config import get_settings

logger = get_logger(__name__)
router = APIRouter(prefix="/api/v1/health", tags=["health"])

# Security schemes for Swagger UI
bearer_scheme = HTTPBearer(auto_error=False)


def get_api_key_auth(
    credentials: HTTPAuthorizationCredentials = Depends(bearer_scheme),
):
    """
    Security dependency for Swagger UI authentication.
    This is mainly for documentation purposes - actual auth is handled by middleware.
    """
    return credentials


# Dependency to get trigger manager instance
def get_trigger_manager() -> TriggerManager:
    """Get trigger manager instance."""
    if not hasattr(get_trigger_manager, "_instance"):
        from src.adapters import GoogleCalendarAdapter

        get_trigger_manager._instance = TriggerManager()

        # Register available adapters
        google_calendar_adapter = GoogleCalendarAdapter()
        get_trigger_manager._instance.register_adapter(google_calendar_adapter)

        logger.info("TriggerManager singleton created with registered adapters")
    return get_trigger_manager._instance


@router.get("/")
async def basic_health_check() -> Dict[str, Any]:
    """
    Basic health check endpoint.

    This endpoint provides a simple health status without authentication
    and is suitable for load balancer health checks.

    Returns:
        Dict[str, Any]: Basic health status
    """
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "trigger-service",
        "version": "1.0.0",
    }


@router.get("/detailed", dependencies=[Depends(get_api_key_auth)])
async def detailed_health_check(
    request: Request, trigger_manager: TriggerManager = Depends(get_trigger_manager)
) -> Dict[str, Any]:
    """
    Detailed health check with dependency status.

    This endpoint requires authentication and provides detailed health
    information including database and adapter status.

    Args:
        request: HTTP request for authentication
        trigger_manager: Trigger manager instance

    Returns:
        Dict[str, Any]: Detailed health status

    Raises:
        HTTPException: If authentication fails
    """

    health_status = {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "trigger-service",
        "version": "1.0.0",
        "dependencies": {},
    }

    # Check database health
    try:
        db_healthy = await db_manager.health_check()
        health_status["dependencies"]["database"] = {
            "status": "healthy" if db_healthy else "unhealthy",
            "checked_at": datetime.now().isoformat(),
        }
    except Exception as e:
        health_status["dependencies"]["database"] = {
            "status": "unhealthy",
            "error": str(e),
            "checked_at": datetime.now().isoformat(),
        }
        health_status["status"] = "degraded"

    # Check adapter health
    try:
        adapter_health = await trigger_manager.health_check()
        health_status["dependencies"]["adapters"] = {}

        all_adapters_healthy = True
        for adapter_name, adapter_status in adapter_health.items():
            health_status["dependencies"]["adapters"][adapter_name] = {
                "status": "healthy" if adapter_status.is_healthy else "unhealthy",
                "last_check": adapter_status.last_check.isoformat(),
                "error_message": adapter_status.error_message,
            }
            if not adapter_status.is_healthy:
                all_adapters_healthy = False

        if not all_adapters_healthy and health_status["status"] == "healthy":
            health_status["status"] = "degraded"

    except Exception as e:
        health_status["dependencies"]["adapters"] = {
            "status": "unhealthy",
            "error": str(e),
            "checked_at": datetime.now().isoformat(),
        }
        health_status["status"] = "degraded"

    return health_status


@router.get(
    "/adapters",
    response_model=List[AdapterHealthResponse],
    dependencies=[Depends(get_api_key_auth)],
)
async def adapter_health_check(
    request: Request, trigger_manager: TriggerManager = Depends(get_trigger_manager)
) -> List[AdapterHealthResponse]:
    """
    Get health status for all registered adapters.

    Args:
        request: HTTP request for authentication
        trigger_manager: Trigger manager instance

    Returns:
        List[AdapterHealthResponse]: List of adapter health statuses

    Raises:
        HTTPException: If authentication fails or health check fails
    """

    try:
        adapter_health = await trigger_manager.health_check()

        health_responses = []
        for adapter_name, health_status in adapter_health.items():
            # Get adapter statistics
            adapter = trigger_manager.get_adapter(adapter_name)
            active_triggers = adapter.get_trigger_count() if adapter else 0

            health_response = AdapterHealthResponse(
                adapter_name=adapter_name,
                is_healthy=health_status.is_healthy,
                last_check=health_status.last_check,
                error_message=health_status.error_message,
                active_triggers=active_triggers,
                external_service_status=health_status.external_service_status,
            )
            health_responses.append(health_response)

        return health_responses

    except Exception as e:
        logger.error(f"Failed to get adapter health status", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve adapter health status",
        )
