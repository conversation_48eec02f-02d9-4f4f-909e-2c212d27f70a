"""
Webhook API routes for receiving external service events.

This module contains webhook endpoints for receiving events from external
services like Google Calendar, Slack, etc.
"""

from fastapi import (
    APIRouter,
    Request,
    HTTPException,
    status,
    BackgroundTasks,
    Depends,
    Query,
)
from typing import Dict, Any, Optional
import json

from src.core.trigger_manager import TriggerManager
from src.utils.logger import get_logger
from src.api.middleware.correlation import get_correlation_id

logger = get_logger(__name__)
router = APIRouter(prefix="/api/v1/webhooks", tags=["webhooks"])


# Dependency to get trigger manager instance
def get_trigger_manager() -> TriggerManager:
    """Get trigger manager instance."""
    if not hasattr(get_trigger_manager, "_instance"):
        from src.adapters import GoogleCalendarAdapter

        get_trigger_manager._instance = TriggerManager()

        # Register available adapters
        google_calendar_adapter = GoogleCalendarAdapter()
        get_trigger_manager._instance.register_adapter(google_calendar_adapter)

        logger.info("TriggerManager singleton created with registered adapters")
    return get_trigger_manager._instance


async def process_webhook_event(
    adapter_name: str,
    event_data: Dict[str, Any],
    trigger_manager: TriggerManager,
    correlation_id: Optional[str] = None,
) -> bool:
    """
    Process a webhook event in the background.

    Args:
        adapter_name: Name of the adapter to process the event
        event_data: Raw event data from the webhook
        trigger_manager: Trigger manager instance
        correlation_id: Correlation ID for tracking

    Returns:
        bool: True if event was processed successfully
    """
    try:
        logger.info(
            f"Processing webhook event for {adapter_name}",
            correlation_id=correlation_id,
            event_type=event_data.get("type", "unknown"),
        )

        success = await trigger_manager.process_event(adapter_name, event_data)

        if success:
            logger.info(
                f"Successfully processed webhook event for {adapter_name} {event_data}",
                correlation_id=correlation_id,
            )
        else:
            logger.warning(
                f"Webhook event processing returned false for {adapter_name}",
                correlation_id=correlation_id,
            )

        return success

    except Exception as e:
        logger.error(
            f"Failed to process webhook event for {adapter_name}",
            correlation_id=correlation_id,
            error=str(e),
        )
        return False


@router.post("/google-calendar")
async def google_calendar_webhook(
    request: Request,
    background_tasks: BackgroundTasks,
    trigger_manager: TriggerManager = Depends(get_trigger_manager),
) -> Dict[str, str]:
    """
    Webhook endpoint for Google Calendar events.

    This endpoint receives webhook notifications from Google Calendar and processes them
    based on the `x-goog-resource-state` header to distinguish between verification (sync)
    and event change (notification) events.

    Args:
        request: HTTP request containing the webhook payload
        background_tasks: FastAPI background tasks for asynchronous processing
        trigger_manager: Trigger manager instance for event processing

    Returns:
        Dict[str, str]: Success response indicating the webhook was received and queued

    Raises:
        HTTPException: If an error occurs during webhook processing
    """

    correlation_id = get_correlation_id(request)

    try:
        # Get the raw body of the request
        body = await request.body()

        # Parse headers for Google Calendar webhook validation
        headers = dict(request.headers)

        # Log the incoming webhook for debugging purposes
        logger.info(
            "Received Google Calendar webhook",
            correlation_id=correlation_id,
            headers=headers,
            body_size=len(body),
        )

        # Extract the resource state from headers to determine the event type
        resource_state = headers.get("x-goog-resource-state", "")

        # Determine the event type based on the resource_state
        if resource_state == "sync":
            event_type = "verification"
        elif resource_state in ["exists", "not_exists"]:
            event_type = "notification"
        else:
            event_type = "unknown"

        # Parse the event data from the request body
        try:
            if body:
                event_data = json.loads(body.decode("utf-8"))
            else:
                event_data = {}
        except json.JSONDecodeError as e:
            logger.warning(
                f"Failed to parse Google Calendar webhook body as JSON: {str(e)}",
                correlation_id=correlation_id,
            )
            event_data = {
                "type": "unparseable",
                "raw_body": body.decode("utf-8", errors="ignore"),
            }

        # Add the determined event type and headers to the event_data dictionary
        event_data["type"] = event_type
        event_data["webhook_headers"] = headers

        # Queue the event for processing in the background
        background_tasks.add_task(
            process_webhook_event,
            "google_calendar",
            event_data,
            trigger_manager,
            correlation_id,
        )

        # Return a success response to acknowledge receipt of the webhook
        return {
            "status": "accepted",
            "message": "Webhook received and queued for processing",
        }

    except Exception as e:
        logger.error(
            f"Failed to handle Google Calendar webhook",
            correlation_id=correlation_id,
            error=str(e),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process webhook",
        )


@router.get("/google-calendar/verify")
async def google_calendar_webhook_verify(
    request: Request, challenge: Optional[str] = Query(None)
) -> Dict[str, str]:
    """
    Webhook verification endpoint for Google Calendar.

    Args:
        request: HTTP request
        challenge: Challenge parameter for verification

    Returns:
        Dict[str, str]: Verification response
    """
    correlation_id = get_correlation_id(request)

    logger.info(
        "Google Calendar webhook verification request",
        correlation_id=correlation_id,
        challenge=challenge,
    )

    if challenge:
        return {"challenge": challenge}
    else:
        return {
            "status": "verified",
            "message": "Google Calendar webhook endpoint is active",
        }


@router.post("/generic/{adapter_name}")
async def generic_webhook(
    adapter_name: str,
    request: Request,
    background_tasks: BackgroundTasks,
    trigger_manager: TriggerManager = Depends(get_trigger_manager),
) -> Dict[str, str]:
    """
    Generic webhook endpoint for any adapter type.

    Args:
        adapter_name: Name of the adapter to process the event
        request: HTTP request containing the webhook payload
        background_tasks: FastAPI background tasks
        trigger_manager: Trigger manager instance

    Returns:
        Dict[str, str]: Success response

    Raises:
        HTTPException: If webhook processing fails
    """
    correlation_id = get_correlation_id(request)

    try:
        # Get the raw body
        body = await request.body()

        # Parse headers
        headers = dict(request.headers)

        # Log the incoming webhook
        logger.info(
            f"Received generic webhook for {adapter_name}",
            correlation_id=correlation_id,
            headers=headers,
            body_size=len(body),
        )

        # Parse the event data
        try:
            if body:
                event_data = json.loads(body.decode("utf-8"))
            else:
                event_data = {"type": "empty_body", "headers": headers}
        except json.JSONDecodeError as e:
            logger.warning(
                f"Failed to parse webhook body as JSON for {adapter_name}: {str(e)}",
                correlation_id=correlation_id,
            )
            event_data = {
                "type": "unparseable",
                "raw_body": body.decode("utf-8", errors="ignore"),
                "headers": headers,
            }

        # Add metadata
        event_data["webhook_headers"] = headers
        event_data["adapter_name"] = adapter_name

        # Process the event in the background
        background_tasks.add_task(
            process_webhook_event,
            adapter_name,
            event_data,
            trigger_manager,
            correlation_id,
        )

        return {
            "status": "accepted",
            "message": f"Webhook received for {adapter_name} and queued for processing",
        }

    except Exception as e:
        logger.error(
            f"Failed to handle generic webhook for {adapter_name}",
            correlation_id=correlation_id,
            error=str(e),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process webhook",
        )


@router.get("/status")
async def webhook_status() -> Dict[str, Any]:
    """
    Get webhook endpoint status and information.

    Returns:
        Dict[str, Any]: Webhook status information
    """
    return {
        "status": "active",
        "message": "Webhook endpoints are operational",
        "endpoints": {
            "google_calendar": {
                "webhook": "/api/v1/webhooks/google-calendar",
                "verify": "/api/v1/webhooks/google-calendar/verify",
            },
            "generic": "/api/v1/webhooks/generic/{adapter_name}",
        },
        "supported_adapters": ["google_calendar", "slack", "github", "custom"],
    }
