"""
Main application entry point for the Trigger Service.

This module initializes and configures the FastAPI application with all
necessary middleware, routes, and startup/shutdown events.
"""

import uvicorn
from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, APIKeyHeader
from contextlib import asynccontextmanager

from src.utils.config import get_settings
from src.utils.logger import setup_logging, get_logger
from src.database.connection import init_database, close_database
from src.api.routes import triggers, webhooks, health, google_calendar
from src.api.middleware.error_handler import ErrorHandlerMiddleware
from src.api.middleware.correlation import CorrelationMiddleware
from src.api.middleware.logging import LoggingMiddleware, PerformanceLoggingMiddleware

logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager for startup and shutdown events.

    Args:
        app: FastAPI application instance
    """
    # Startup
    settings = get_settings()
    setup_logging(settings.log_level, settings.log_format)

    try:
        await init_database()

        # Load existing webhook subscriptions
        await load_existing_webhook_subscriptions()

        yield
    finally:
        # Shutdown
        await close_database()


async def load_existing_webhook_subscriptions():
    """Load existing webhook subscriptions and register them with adapters."""
    try:
        import json
        import os
        from datetime import datetime, timezone
        from uuid import uuid4
        from src.adapters.base import TriggerEventType

        # Check if webhook subscription file exists
        subscription_file = "webhook_subscription.json"
        if not os.path.exists(subscription_file):
            logger.info("No existing webhook subscription file found")
            return

        # Load subscription info
        with open(subscription_file, "r") as f:
            subscription_info = json.load(f)

        # Check if subscription is still valid
        expires_at = datetime.fromisoformat(
            subscription_info["expires_at"].replace("Z", "+00:00")
        )
        now = datetime.now(timezone.utc)

        if expires_at <= now:
            logger.warning(f"Webhook subscription has expired: {expires_at}")
            return

        # Get the Google Calendar adapter from the trigger manager
        from src.api.routes.webhooks import get_trigger_manager

        trigger_manager = get_trigger_manager()

        # Find the Google Calendar adapter
        google_calendar_adapter = None
        for adapter_name, adapter in trigger_manager.adapters.items():
            if adapter_name == "google_calendar":
                google_calendar_adapter = adapter
                break

        if not google_calendar_adapter:
            logger.error("Google Calendar adapter not found in trigger manager")
            return

        # Register the channel with the adapter
        channel_id = subscription_info["channel_id"]
        resource_id = subscription_info["resource_id"]
        calendar_id = subscription_info["calendar_id"]
        expiration_ms = int(expires_at.timestamp() * 1000)

        google_calendar_adapter._active_channels[channel_id] = {
            "resource_id": resource_id,
            "expiration": expiration_ms,
            "trigger_id": uuid4(),  # Generate a dummy trigger ID
            "user_id": "test-user",  # Default user ID
            "calendar_id": calendar_id,
            "event_types": [
                TriggerEventType.CREATED,
                TriggerEventType.UPDATED,
                TriggerEventType.DELETED,
            ],
            "created_at": datetime.now(timezone.utc).timestamp(),
        }

        logger.info(
            f"Loaded existing webhook subscription",
            channel_id=channel_id,
            resource_id=resource_id,
            calendar_id=calendar_id,
            expires_at=expires_at.isoformat(),
        )

    except Exception as e:
        logger.error(f"Failed to load existing webhook subscriptions: {e}")


def create_app() -> FastAPI:
    """
    Create and configure the FastAPI application.

    Returns:
        FastAPI: Configured application instance
    """
    settings = get_settings()

    # Create FastAPI app with lifespan and security schemes
    app = FastAPI(
        title="Workflow Automation Platform - Trigger Service",
        description="""
        A comprehensive trigger service for workflow automation platform.

        This service manages triggers that monitor external events and initiate workflows.
        It supports multiple adapters including Google Calendar, webhooks, and more.

        ## Features
        - **Multi-user Google Calendar integration** with webhook and polling support
        - **Real-time webhook processing** with signature verification
        - **Polling-based event monitoring** as fallback
        - **Dead letter queue** for failed events with retry capabilities
        - **Multi-user credential management** with caching
        - **Webhook channel lifecycle management** with automatic cleanup
        - **Comprehensive metrics and monitoring**

        ## Authentication
        This API supports two authentication methods:
        1. **Bearer Token**: Use `Authorization: Bearer <token>` header
        2. **API Key**: Use `X-API-Key: <key>` header

        Both methods are supported on all protected endpoints. Use the "Authorize" button
        in Swagger UI to test with either method.

        ## Google Calendar Integration
        The Google Calendar adapter supports:
        - **Multi-user webhook subscriptions** with automatic user routing
        - **Polling mode fallback** when webhooks are not available
        - **Per-user credential management** with token refresh
        - **Channel expiration handling** and cleanup
        - **Signature verification** for webhook security
        """,
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc",
        lifespan=lifespan,
        debug=settings.debug,
        # Add security schemes for Swagger UI
        openapi_tags=[
            {
                "name": "triggers",
                "description": "Trigger management operations - create, update, delete, and monitor triggers",
            },
            {
                "name": "webhooks",
                "description": "Webhook processing endpoints for external services",
            },
            {
                "name": "Google Calendar",
                "description": "Google Calendar adapter management - webhook channels, status, and cleanup",
            },
            {
                "name": "health",
                "description": "Health checks and service status monitoring",
            },
        ],
    )

    # Define security schemes for Swagger UI
    from fastapi.openapi.utils import get_openapi

    def custom_openapi():
        if app.openapi_schema:
            return app.openapi_schema

        openapi_schema = get_openapi(
            title=app.title,
            version=app.version,
            description=app.description,
            routes=app.routes,
        )

        # Add security schemes
        openapi_schema["components"]["securitySchemes"] = {
            "BearerAuth": {
                "type": "http",
                "scheme": "bearer",
                "bearerFormat": "API Key",
                "description": "Enter your API key as a Bearer token (e.g., 'abc' for development)",
            },
            "ApiKeyAuth": {
                "type": "apiKey",
                "in": "header",
                "name": "X-API-Key",
                "description": "Enter your API key in the X-API-Key header (e.g., 'abc' for development)",
            },
        }

        # Add security requirements to protected endpoints
        for path_item in openapi_schema["paths"].values():
            for operation in path_item.values():
                if isinstance(operation, dict) and "tags" in operation:
                    # Add security to all endpoints except health and webhooks
                    if (
                        operation.get("tags")
                        and "health" not in operation["tags"]
                        and "webhooks" not in operation["tags"]
                    ):
                        operation["security"] = [{"BearerAuth": []}, {"ApiKeyAuth": []}]

        app.openapi_schema = openapi_schema
        return app.openapi_schema

    app.openapi = custom_openapi

    # Add middleware in correct order (last added = first executed)
    # CORS middleware should be last (first to execute)
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Allow all origins for testing
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        allow_headers=["*"],
    )

    # Add exception handlers
    @app.exception_handler(404)
    async def not_found_handler(request: Request, exc):
        """Handle 404 errors with consistent format."""
        return JSONResponse(
            status_code=404,
            content={
                "error": {
                    "type": "NotFound",
                    "message": "The requested resource was not found",
                    "status_code": 404,
                }
            },
        )

    @app.exception_handler(500)
    async def internal_error_handler(request: Request, exc):
        """Handle 500 errors with consistent format."""
        logger.error(f"Internal server error: {str(exc)}")
        return JSONResponse(
            status_code=500,
            content={
                "error": {
                    "type": "InternalServerError",
                    "message": "An internal server error occurred",
                    "status_code": 500,
                }
            },
        )

    # Register routers
    app.include_router(health.router)
    app.include_router(triggers.router)
    app.include_router(webhooks.router)
    app.include_router(google_calendar.router)

    # Add root endpoint
    @app.get("/")
    async def root():
        """Root endpoint with service information."""
        return {
            "service": "trigger-service",
            "version": "1.0.0",
            "status": "operational",
            "docs": "/docs",
            "health": "/api/v1/health",
        }

    logger.info("FastAPI application created and configured")
    return app


def get_app() -> FastAPI:
    """
    Get the FastAPI application instance.

    This function creates the app on-demand to avoid loading settings
    at module import time.

    Returns:
        FastAPI: Application instance
    """
    return create_app()


# Create app instance for uvicorn when running as module
app = get_app()


def main() -> None:
    """Main entry point for the application."""
    settings = get_settings()
    uvicorn.run(
        "src.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
    )


if __name__ == "__main__":
    main()
