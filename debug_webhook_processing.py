#!/usr/bin/env python3
"""
Debug script to investigate why webhook events are not being fetched and saved in the database.
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime, timezone

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.database.connection import get_async_session
from src.database.models import CalendarEvent
from src.adapters.google_calendar import GoogleCalendarAdapter
from sqlalchemy import select, desc, text


async def check_database_state():
    """Check the current state of the database."""
    print("🔍 Checking database state...")
    
    try:
        async for session in get_async_session():
            # Check calendar_events table
            result = await session.execute(select(CalendarEvent).order_by(desc(CalendarEvent.created_at)))
            events = result.scalars().all()
            
            print(f"\n📊 Found {len(events)} calendar events in database:")
            
            for i, event in enumerate(events, 1):
                print(f"\n{i}. Event ID: {event.event_id}")
                print(f"   User ID: {event.user_id}")
                print(f"   Calendar ID: {event.calendar_id}")
                print(f"   Channel ID: {event.channel_id}")
                print(f"   Resource ID: {event.resource_id}")
                print(f"   Event Type: {event.event_type}")
                print(f"   Created At: {event.created_at}")
                print(f"   Google Created At: {event.google_created_at}")
                print(f"   Event Summary: {event.event_data.get('summary', 'No title')}")
                
                if i >= 5:  # Limit to first 5 events
                    print(f"   ... and {len(events) - 5} more events")
                    break
            
            break  # Exit after first iteration
            
    except Exception as e:
        print(f"❌ Error checking database: {e}")


async def check_adapter_state():
    """Check the Google Calendar adapter state."""
    print("\n🔧 Checking Google Calendar adapter state...")
    
    try:
        adapter = GoogleCalendarAdapter()
        
        print(f"📋 Adapter Info:")
        print(f"   Name: {adapter.name}")
        print(f"   Supported Event Types: {[et.value for et in adapter.supported_event_types]}")
        print(f"   Active Channels: {len(adapter._active_channels)}")
        print(f"   Webhook Subscriptions: {len(adapter._webhook_subscriptions)}")
        
        if adapter._active_channels:
            print(f"\n📡 Active Channels:")
            for channel_id, channel_info in adapter._active_channels.items():
                print(f"   - Channel ID: {channel_id}")
                print(f"     User ID: {channel_info.get('user_id', 'Unknown')}")
                print(f"     Calendar ID: {channel_info.get('calendar_id', 'Unknown')}")
                print(f"     Trigger ID: {channel_info.get('trigger_id', 'Unknown')}")
                print(f"     Resource ID: {channel_info.get('resource_id', 'Unknown')}")
        else:
            print(f"\n⚠️ No active channels found!")
            print(f"   This means no webhook subscriptions are registered.")
            print(f"   Webhook events will be ignored because the channel is unknown.")
        
        # Test health check
        is_healthy = await adapter._perform_health_check()
        print(f"\n🏥 Health Check: {'✅ PASS' if is_healthy else '❌ FAIL'}")
        
    except Exception as e:
        print(f"❌ Error checking adapter: {e}")


async def simulate_webhook_processing():
    """Simulate webhook processing to identify issues."""
    print("\n🧪 Simulating webhook processing...")
    
    try:
        adapter = GoogleCalendarAdapter()
        
        # Mock webhook event data (similar to what Google Calendar sends)
        mock_webhook_event = {
            "type": "notification",
            "webhook_headers": {
                "x-goog-channel-id": "test-channel-debug",
                "x-goog-resource-id": "test-resource-debug", 
                "x-goog-resource-state": "exists",
                "x-goog-resource-uri": "https://www.googleapis.com/calendar/v3/calendars/primary/events",
                "x-goog-message-number": "1"
            }
        }
        
        print(f"📨 Processing mock webhook event...")
        print(f"   Channel ID: {mock_webhook_event['webhook_headers']['x-goog-channel-id']}")
        print(f"   Resource State: {mock_webhook_event['webhook_headers']['x-goog-resource-state']}")
        
        # Process the event
        result = await adapter.process_event(mock_webhook_event)
        
        print(f"📊 Processing Result: {result}")
        
        if result is None:
            print(f"⚠️ Event processing returned None - this is expected if:")
            print(f"   1. No active channels are registered")
            print(f"   2. No credentials are available")
            print(f"   3. Channel ID is unknown")
        
    except Exception as e:
        print(f"❌ Error simulating webhook processing: {e}")


async def check_recent_logs():
    """Check for recent log entries that might indicate issues."""
    print("\n📝 Checking for common issues...")
    
    issues_found = []
    
    # Check if there are active channels
    adapter = GoogleCalendarAdapter()
    if not adapter._active_channels:
        issues_found.append("❌ No active webhook channels registered")
    
    # Check database connectivity
    try:
        async for session in get_async_session():
            await session.execute(text("SELECT 1"))
            break
        print("✅ Database connectivity: OK")
    except Exception as e:
        issues_found.append(f"❌ Database connectivity issue: {e}")
    
    # Check if credentials are available
    try:
        credentials = await adapter._get_user_credentials("test-user")
        if credentials:
            print("✅ Credentials loading: OK")
        else:
            issues_found.append("❌ No credentials found for test user")
    except Exception as e:
        issues_found.append(f"❌ Credential loading error: {e}")
    
    if issues_found:
        print(f"\n🚨 Issues Found:")
        for issue in issues_found:
            print(f"   {issue}")
    else:
        print(f"\n✅ No obvious issues detected")


async def main():
    """Main diagnostic function."""
    print("🔍 Google Calendar Webhook Processing Diagnostic")
    print("=" * 60)
    
    await check_database_state()
    await check_adapter_state()
    await simulate_webhook_processing()
    await check_recent_logs()
    
    print("\n" + "=" * 60)
    print("🎯 DIAGNOSIS SUMMARY:")
    print("=" * 60)
    
    print("\n💡 Common reasons why webhook events might not be saved:")
    print("1. ❌ No active webhook channels registered")
    print("   - Solution: Register webhook subscriptions using register_webhook.py")
    print()
    print("2. ❌ Webhook channel ID doesn't match registered channels")
    print("   - Solution: Check webhook registration and ensure channel IDs match")
    print()
    print("3. ❌ No Google Calendar credentials available")
    print("   - Solution: Ensure token.json exists and is valid")
    print()
    print("4. ❌ Google Calendar API calls failing")
    print("   - Solution: Check API quotas, credentials, and network connectivity")
    print()
    print("5. ❌ Database connection issues")
    print("   - Solution: Verify PostgreSQL is running and accessible")
    print()
    print("6. ❌ Events are too old (older than 5 minutes)")
    print("   - Solution: The system only fetches events from the last 5 minutes")
    print()
    
    print("\n🔧 Next Steps:")
    print("1. Check the FastAPI server logs for detailed error messages")
    print("2. Verify webhook registration status")
    print("3. Test with a fresh calendar event creation")
    print("4. Check Google Calendar API quotas and limits")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Diagnostic interrupted by user")
    except Exception as e:
        print(f"❌ Diagnostic failed: {e}")
        sys.exit(1)
