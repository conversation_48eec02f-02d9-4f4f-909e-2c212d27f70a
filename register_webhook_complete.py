#!/usr/bin/env python3
"""
Complete Google Calendar webhook registration that registers both with Google Calendar API
and the local FastAPI application adapter.
"""

import asyncio
import json
import os
import sys
import uuid
from datetime import datetime, timezone
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from src.adapters.google_calendar import GoogleCalendarAdapter
from src.adapters.base import TriggerEventType

# Configuration
WEBHOOK_URL = "https://99b5-103-173-221-201.ngrok-free.app/api/v1/webhooks/google-calendar"
CALENDAR_ID = "primary"
WEBHOOK_TTL = 604800  # 7 days
USER_ID = "test-user"  # Default user ID for testing


def load_credentials():
    """Load Google Calendar API credentials"""
    print("🔐 Loading Google Calendar credentials...")

    token_paths = ["token.json", "./token.json", "../token.json", "../../token.json"]

    token_file = None
    for path in token_paths:
        if os.path.exists(path):
            token_file = path
            print(f"   Found credentials: {path}")
            break

    if not token_file:
        print("❌ No token.json file found!")
        return None

    try:
        creds = Credentials.from_authorized_user_file(token_file)

        if not creds.valid:
            if creds.expired and creds.refresh_token:
                print("🔄 Refreshing expired credentials...")
                creds.refresh(Request())
                with open(token_file, "w") as f:
                    f.write(creds.to_json())
                print("✅ Credentials refreshed")
            else:
                print("❌ Credentials are invalid and cannot be refreshed")
                return None

        print("✅ Credentials loaded successfully")
        return creds

    except Exception as e:
        print(f"❌ Error loading credentials: {e}")
        return None


def test_webhook_endpoint():
    """Test if the webhook endpoint is accessible"""
    print("🌐 Testing webhook endpoint accessibility...")

    try:
        import requests

        # Test the webhook endpoint directly
        response = requests.get(
            WEBHOOK_URL.replace("/google-calendar", "/google-calendar/verify"),
            timeout=10
        )

        if response.status_code == 200:
            print("✅ Webhook endpoint is accessible")
            return True
        else:
            print(f"❌ Webhook endpoint returned status: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ Cannot reach webhook endpoint: {e}")
        print("\n💡 Make sure:")
        print("   1. Your FastAPI server is running")
        print("   2. Your ngrok tunnel is active")
        print("   3. The ngrok URL matches the one in this script")
        return False


def register_with_google_calendar(service):
    """Register webhook subscription with Google Calendar API"""
    print("📝 Registering webhook with Google Calendar API...")

    try:
        # Generate unique channel ID
        channel_id = f"complete-webhook-{str(uuid.uuid4())[:8]}"

        # Calculate expiration
        expiration_timestamp = datetime.now(timezone.utc).timestamp() + WEBHOOK_TTL
        expiration_ms = int(expiration_timestamp * 1000)

        # Create watch request
        watch_request = {
            "id": channel_id,
            "type": "web_hook",
            "address": WEBHOOK_URL,
            "expiration": str(expiration_ms),
        }

        print(f"   Channel ID: {channel_id}")
        print(f"   Webhook URL: {WEBHOOK_URL}")
        print(f"   Calendar ID: {CALENDAR_ID}")
        print(f"   Expires: {datetime.fromtimestamp(expiration_timestamp, timezone.utc)}")

        # Execute the watch request
        print("\n🚀 Sending registration request to Google Calendar API...")
        result = service.events().watch(calendarId=CALENDAR_ID, body=watch_request).execute()

        print("\n🎉 SUCCESS! Webhook registered with Google Calendar!")
        print("=" * 50)
        print(f"Channel ID: {result.get('id')}")
        print(f"Resource ID: {result.get('resourceId')}")
        print(f"Expiration: {datetime.fromtimestamp(int(result.get('expiration', 0))/1000, timezone.utc)}")

        return {
            "channel_id": result.get("id"),
            "resource_id": result.get("resourceId"),
            "expiration": int(result.get("expiration", 0)),
            "webhook_url": WEBHOOK_URL,
            "calendar_id": CALENDAR_ID,
        }

    except HttpError as e:
        print(f"\n❌ Google Calendar API Error: {e}")
        return None
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return None


async def register_with_local_adapter(webhook_info):
    """Register the webhook channel with the local Google Calendar adapter"""
    print("\n🔧 Registering webhook with local FastAPI adapter...")

    try:
        # Create adapter instance
        adapter = GoogleCalendarAdapter()

        # Prepare channel info for the adapter
        channel_id = webhook_info["channel_id"]
        resource_id = webhook_info["resource_id"]
        expiration = webhook_info["expiration"]

        # Register the channel with the adapter
        adapter._active_channels[channel_id] = {
            "resource_id": resource_id,
            "expiration": expiration,
            "trigger_id": uuid.uuid4(),  # Generate a dummy trigger ID for testing
            "user_id": USER_ID,
            "calendar_id": CALENDAR_ID,
            "event_types": [
                TriggerEventType.CREATED,
                TriggerEventType.UPDATED,
                TriggerEventType.DELETED,
            ],
            "created_at": datetime.now(timezone.utc).timestamp(),
        }

        print(f"✅ Channel registered with local adapter!")
        print(f"   Channel ID: {channel_id}")
        print(f"   Resource ID: {resource_id}")
        print(f"   User ID: {USER_ID}")
        print(f"   Calendar ID: {CALENDAR_ID}")

        # Verify registration
        if channel_id in adapter._active_channels:
            print(f"✅ Verification: Channel found in active channels")
            return True
        else:
            print(f"❌ Verification: Channel not found in active channels")
            return False

    except Exception as e:
        print(f"❌ Error registering with local adapter: {e}")
        return False


def save_subscription_info(webhook_info):
    """Save subscription information for future reference"""
    print("\n💾 Saving subscription information...")

    try:
        subscription_info = {
            "channel_id": webhook_info["channel_id"],
            "resource_id": webhook_info["resource_id"],
            "webhook_url": webhook_info["webhook_url"],
            "calendar_id": webhook_info["calendar_id"],
            "user_id": USER_ID,
            "created_at": datetime.now(timezone.utc).isoformat(),
            "expires_at": datetime.fromtimestamp(
                webhook_info["expiration"] / 1000, timezone.utc
            ).isoformat(),
        }

        with open("webhook_subscription.json", "w") as f:
            json.dump(subscription_info, f, indent=2)

        print(f"✅ Subscription info saved to: webhook_subscription.json")
        return True

    except Exception as e:
        print(f"❌ Error saving subscription info: {e}")
        return False


async def main():
    print("🚀 Complete Google Calendar Webhook Registration Tool")
    print("=" * 70)
    print(f"Timestamp: {datetime.now()}")
    print(f"Target URL: {WEBHOOK_URL}")
    print(f"User ID: {USER_ID}")
    print()

    # Step 1: Test webhook endpoint
    if not test_webhook_endpoint():
        print("\n❌ CRITICAL: Cannot reach webhook endpoint!")
        print("Please fix the endpoint accessibility before proceeding.")
        return False

    print()

    # Step 2: Load credentials
    creds = load_credentials()
    if not creds:
        print("\n❌ CRITICAL: Cannot load Google Calendar credentials!")
        return False

    print()

    # Step 3: Create Calendar service
    try:
        service = build("calendar", "v3", credentials=creds)
        print("✅ Google Calendar service created")
    except Exception as e:
        print(f"❌ Error creating Calendar service: {e}")
        return False

    print()

    # Step 4: Register with Google Calendar API
    webhook_info = register_with_google_calendar(service)
    if not webhook_info:
        print("\n❌ CRITICAL: Failed to register with Google Calendar API!")
        return False

    # Step 5: Register with local adapter
    local_success = await register_with_local_adapter(webhook_info)
    if not local_success:
        print("\n❌ CRITICAL: Failed to register with local adapter!")
        return False

    # Step 6: Save subscription info
    save_subscription_info(webhook_info)

    print("\n🎉 COMPLETE WEBHOOK REGISTRATION SUCCESS!")
    print("=" * 70)
    print("\n📋 What was registered:")
    print(f"   ✅ Google Calendar API webhook subscription")
    print(f"   ✅ Local FastAPI adapter channel registration")
    print(f"   ✅ Subscription info saved to file")

    print("\n🧪 Test the webhook:")
    print("   1. Go to Google Calendar")
    print("   2. Create, update, or delete a calendar event")
    print("   3. Check your FastAPI logs for webhook notifications")
    print("   4. Check the database for stored calendar events")

    print("\n🔍 Debug commands:")
    print("   python debug_webhook_processing.py  # Check webhook status")
    print("   python test_webhook_endpoint.py     # Test endpoint directly")

    return True


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        if success:
            print("\n✅ All done! Your Google Calendar webhook is now fully active.")
        else:
            print("\n❌ Setup incomplete. Please resolve the issues and try again.")
    except KeyboardInterrupt:
        print("\n🛑 Registration interrupted by user")
    except Exception as e:
        print(f"\n❌ Registration failed: {e}")
        sys.exit(1)
