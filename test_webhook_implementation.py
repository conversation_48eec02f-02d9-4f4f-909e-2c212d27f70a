#!/usr/bin/env python3
"""
Test script for the enhanced Google Calendar webhook implementation.
"""

import asyncio
import json
import sys
from pathlib import Path
from datetime import datetime, timezone

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.adapters.google_calendar import GoogleCalendarAdapter
from src.adapters.base import TriggerConfiguration, TriggerEventType
from src.database.connection import get_async_session
from src.database.models import CalendarEvent
from sqlalchemy import select
from uuid import uuid4


async def test_webhook_processing():
    """Test the webhook processing functionality."""
    print("🧪 Testing Google Calendar webhook implementation...")

    # Create adapter instance
    adapter = GoogleCalendarAdapter()

    # Test configuration validation
    print("\n1. Testing configuration validation...")
    valid_config = {"calendar_id": "primary", "webhook_ttl": 3600, "event_filters": {}}

    is_valid = await adapter.validate_config(valid_config)
    print(f"   Configuration validation: {'✅ PASS' if is_valid else '❌ FAIL'}")

    # Test invalid configuration
    invalid_config = {
        "calendar_id": "",  # Empty calendar ID
    }

    is_invalid = not await adapter.validate_config(invalid_config)
    print(
        f"   Invalid configuration rejection: {'✅ PASS' if is_invalid else '❌ FAIL'}"
    )

    # Test webhook event processing
    print("\n2. Testing webhook event processing...")

    # Mock webhook event data
    mock_webhook_event = {
        "type": "notification",
        "webhook_headers": {
            "x-goog-channel-id": "test-channel-123",
            "x-goog-resource-id": "test-resource-456",
            "x-goog-resource-state": "exists",
            "x-goog-resource-uri": "https://www.googleapis.com/calendar/v3/calendars/primary/events",
            "x-goog-message-number": "1",
        },
    }

    # Test event processing (will fail without credentials, but should handle gracefully)
    trigger_event = await adapter.process_event(mock_webhook_event)
    print(
        f"   Webhook event processing: {'✅ PASS' if trigger_event is None else '❌ UNEXPECTED'}"
    )

    # Test database storage method
    print("\n3. Testing database storage...")

    # Mock calendar event data
    mock_calendar_event = {
        "id": "test-event-123",
        "summary": "Test Event",
        "description": "This is a test event",
        "start": {"dateTime": "2024-01-15T10:00:00Z"},
        "end": {"dateTime": "2024-01-15T11:00:00Z"},
        "created": "2024-01-15T09:00:00Z",
        "updated": "2024-01-15T09:30:00Z",
        "attendees": [{"email": "<EMAIL>", "responseStatus": "accepted"}],
    }

    try:
        # Test storing event in database
        success = await adapter._store_calendar_event_in_database(
            channel_id="test-channel-123",
            resource_id="test-resource-456",
            user_id="test-user-789",
            calendar_id="primary",
            event=mock_calendar_event,
            event_type="created",
        )
        print(f"   Database storage: {'✅ PASS' if success else '❌ FAIL'}")

        # Verify the event was stored
        async for session in get_async_session():
            result = await session.execute(
                select(CalendarEvent).where(CalendarEvent.event_id == "test-event-123")
            )
            stored_event = result.scalar_one_or_none()

            if stored_event:
                print(f"   Database retrieval: ✅ PASS")
                print(
                    f"   Stored event summary: {stored_event.event_data.get('summary')}"
                )
                print(f"   Stored event type: {stored_event.event_type}")
                print(f"   Channel ID: {stored_event.channel_id}")
                print(f"   Resource ID: {stored_event.resource_id}")
            else:
                print(f"   Database retrieval: ❌ FAIL - Event not found")
            break  # Exit after first iteration

    except Exception as e:
        print(f"   Database storage: ❌ FAIL - {e}")

    # Test health check
    print("\n4. Testing health check...")
    is_healthy = await adapter._perform_health_check()
    print(f"   Health check: {'✅ PASS' if is_healthy else '❌ FAIL'}")

    print("\n🎉 Test completed!")


async def cleanup_test_data():
    """Clean up test data from database."""
    try:
        async for session in get_async_session():
            result = await session.execute(
                select(CalendarEvent).where(CalendarEvent.event_id == "test-event-123")
            )
            test_event = result.scalar_one_or_none()

            if test_event:
                await session.delete(test_event)
                await session.commit()
                print("🧹 Cleaned up test data")
            break  # Exit after first iteration
    except Exception as e:
        print(f"⚠️ Failed to clean up test data: {e}")


if __name__ == "__main__":
    try:
        asyncio.run(test_webhook_processing())
        asyncio.run(cleanup_test_data())
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
    except Exception as e:
        print(f"❌ Test failed: {e}")
        sys.exit(1)
